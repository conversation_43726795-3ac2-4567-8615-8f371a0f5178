import React, { useState, useRef } from 'react';
import { RpaStep } from '@rpa-project/shared';
import { VariableHelper, useVariableSuggestions, validateVariableReferences } from './VariableHelper';

interface VariableInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  steps: RpaStep[];
  currentStepIndex: number;
  style?: React.CSSProperties;
  className?: string;
  type?: 'text' | 'url' | 'number';
  required?: boolean;
}

export const VariableInput: React.FC<VariableInputProps> = ({
  value,
  onChange,
  placeholder,
  steps,
  currentStepIndex,
  style,
  className = '',
  type = 'text',
  required = false
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const availableVariables = useVariableSuggestions(steps, currentStepIndex);
  const validation = validateVariableReferences(value, availableVariables);

  const handleInsertVariable = (variableName: string) => {
    const input = inputRef.current;
    if (!input) return;

    const cursorPosition = input.selectionStart || 0;
    const textBefore = value.substring(0, cursorPosition);
    const textAfter = value.substring(cursorPosition);
    const newValue = textBefore + '${' + variableName + '}' + textAfter;
    
    onChange(newValue);
    
    // Set cursor position after the inserted variable
    setTimeout(() => {
      const newCursorPosition = cursorPosition + variableName.length + 3; // ${} = 3 chars
      input.setSelectionRange(newCursorPosition, newCursorPosition);
      input.focus();
    }, 0);
  };

  const inputStyle: React.CSSProperties = {
    ...style,
    borderColor: !validation.valid ? '#ef4444' : style?.borderColor || '#d1d5db'
  };

  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <input
            ref={inputRef}
            type={type}
            className={`form-input ${className}`}
            style={inputStyle}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            required={required}
          />
          
          {!validation.valid && validation.missingVariables.length > 0 && (
            <div style={{
              fontSize: '0.75rem',
              color: '#ef4444',
              marginTop: '0.25rem'
            }}>
              ⚠️ Okända variabler: {validation.missingVariables.join(', ')}
            </div>
          )}
        </div>
        
        <VariableHelper
          steps={steps}
          currentStepIndex={currentStepIndex}
          onInsertVariable={handleInsertVariable}
        />
      </div>
      
      {value && value.includes('${') && (
        <div style={{
          fontSize: '0.75rem',
          color: '#6b7280',
          marginTop: '0.25rem',
          padding: '0.25rem',
          backgroundColor: '#f9fafb',
          borderRadius: '0.25rem',
          fontStyle: 'italic'
        }}>
          💡 Variabler kommer att ersättas med faktiska värden under körning
        </div>
      )}
    </div>
  );
};

interface VariableTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  steps: RpaStep[];
  currentStepIndex: number;
  style?: React.CSSProperties;
  className?: string;
  rows?: number;
  required?: boolean;
}

export const VariableTextarea: React.FC<VariableTextareaProps> = ({
  value,
  onChange,
  placeholder,
  steps,
  currentStepIndex,
  style,
  className = '',
  rows = 3,
  required = false
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const availableVariables = useVariableSuggestions(steps, currentStepIndex);
  const validation = validateVariableReferences(value, availableVariables);

  const handleInsertVariable = (variableName: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const cursorPosition = textarea.selectionStart || 0;
    const textBefore = value.substring(0, cursorPosition);
    const textAfter = value.substring(cursorPosition);
    const newValue = textBefore + '${' + variableName + '}' + textAfter;
    
    onChange(newValue);
    
    // Set cursor position after the inserted variable
    setTimeout(() => {
      const newCursorPosition = cursorPosition + variableName.length + 3; // ${} = 3 chars
      textarea.setSelectionRange(newCursorPosition, newCursorPosition);
      textarea.focus();
    }, 0);
  };

  const textareaStyle: React.CSSProperties = {
    ...style,
    borderColor: !validation.valid ? '#ef4444' : style?.borderColor || '#d1d5db'
  };

  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <textarea
            ref={textareaRef}
            className={`form-input ${className}`}
            style={textareaStyle}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            rows={rows}
            required={required}
          />
          
          {!validation.valid && validation.missingVariables.length > 0 && (
            <div style={{
              fontSize: '0.75rem',
              color: '#ef4444',
              marginTop: '0.25rem'
            }}>
              ⚠️ Okända variabler: {validation.missingVariables.join(', ')}
            </div>
          )}
        </div>
        
        <VariableHelper
          steps={steps}
          currentStepIndex={currentStepIndex}
          onInsertVariable={handleInsertVariable}
        />
      </div>
      
      {value && value.includes('${') && (
        <div style={{
          fontSize: '0.75rem',
          color: '#6b7280',
          marginTop: '0.25rem',
          padding: '0.25rem',
          backgroundColor: '#f9fafb',
          borderRadius: '0.25rem',
          fontStyle: 'italic'
        }}>
          💡 Variabler kommer att ersättas med faktiska värden under körning
        </div>
      )}
    </div>
  );
};
